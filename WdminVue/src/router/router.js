import {createRouter, createWebHashHistory} from "vue-router";
import login                                from "../views/login/index.vue";
import Layout                               from "../layout/index.vue";

const routes = [
    {
        path     : "/Login",
        name     : "Login",
        component: login,
    },
    {
        path     : "",
        component: Layout,
        name     : "Home",
        redirect : "Home",
        meta     : {
            requiresAuth: true,
            name        : "首页",
        },
        children : [{
            path     : "/Home",
            name     : "Home",
            component: () => import("../views/home/<USER>"),
            meta     : {
                requiresAuth: true,
                name        : "首页",
                keepAlive   : true
            },
        },],
    },
    {
        path: "",
        name: "<PERSON><PERSON>",
        //redirect : "Entity",
        component: Layout,
        meta     : {
            // requiresAuth: true,
            name: "待办事项",
            //keepAlive:true
        },
        children : [{
            path     : "/Audit",
            name     : "Audit",
            component: () => import("../views/Audits/Audit.vue"),
            meta     : {
                requiresAuth: true,
                name        : "审核待办",
                keepAlive   : false
            },
        }, {
            path     : "/AuditFlow",
            name     : "AuditFlow",
            component: () => import("../views/Audits/AuditFlow.vue"),
            meta     : {
                requiresAuth: true,
                name        : "流程配置",
                keepAlive   : true
            },
        }, {
            path     : "/AuditProgress",
            name     : "AuditProgress",
            component: () => import("../views/Audits/AuditProgress.vue"),
            meta     : {
                requiresAuth: true,
                name        : "审核日志",
                keepAlive   : false
            },
        }, {
            path     : "/AuditOption",
            name     : "AuditOption",
            component: () => import("../views/Audits/AuditOption.vue"),
            meta     : {
                requiresAuth: true,
                name        : "审核选项",
                keepAlive   : true
            },
        },],
    },
    {
        path     : "",
        name     : "Projects",
        component: Layout,
        meta     : {
            // requiresAuth: true,
            name: "项目管理",
            // keepAlive:true
        },
        children : [{
            path     : "/Project",
            name     : "Project",
            component: () => import("../views/Project/Project.vue"),
            meta     : {
                requiresAuth: true,
                name        : "项目列表",
                keepAlive   : true
            },
        },],
    },
    {
        path     : "",
        name     : "Agents",
        component: Layout,
        meta     : {
            // requiresAuth: true,
            name: "代理列表",
            // keepAlive:true
        },
        children : [{
            path     : "/Agent",
            name     : "Agent",
            component: () => import("../views/Agent/Agent.vue"),
            meta     : {
                requiresAuth: true,
                name        : "代理列表",
                keepAlive   : true
            },
        },],
    },
    {
        path     : "",
        name     : "Shops",
        component: Layout,
        meta     : {
            //requiresAuth: true,
            name: "店铺管理",
            //keepAlive   : true
        },
        children : [{
            path     : "/Shop",
            name     : "Shop",
            component: () => import("../views/Shop/Shop.vue"),
            meta     : {
                requiresAuth: true,
                name        : "店铺列表",
                keepAlive   : true
            },
        }, {
            path     : "/Task",
            name     : "Task",
            component: () => import("../views/Task/Task.vue"),
            meta     : {
                requiresAuth: true,
                name        : "命令状态",
                keepAlive   : true
            },
        }, {
            path     : "/Upgrade",
            name     : "Upgrade",
            component: () => import("../views/Upgrade/Upgrade.vue"),
            meta     : {
                requiresAuth: true,
                name        : "升级包列表",
                keepAlive   : true
            },
        }, {
            path     : "/ShopTemplate",
            name     : "ShopTemplate",
            component: () => import("../views/ShopTemplate/ShopTemplate.vue"),
            meta     : {
                requiresAuth: true,
                name        : "自定义列表",
                keepAlive   : true
            },
        }, {
            path     : "/ThemeManagement",
            name     : "ThemeManagement",
            component: () => import("../views/Shop/ThemeManagement.vue"),
            meta     : {
                requiresAuth: true,
                name        : "主题管理",
                keepAlive   : true
            },
        },
                    {
                        path     : "/Orders",
                        name     : "Order",
                        component: () => import("../views/Orders/Order.vue"),
                        meta     : {
                            requiresAuth: true,
                            name        : "订单管理",
                            keepAlive   : true
                        },
                    },
                    {
                        path     : "/Price",
                        name     : "Price",
                        component: () => import("../views/Price/Price.vue"),
                        meta     : {
                            requiresAuth: true,
                            name        : "设备单价",
                            keepAlive   : true
                        },
                    },
        ],
    },

    {
        path     : "",
        name     : "Devices",
        component: Layout,
        meta     : {
            // requiresAuth: true,
            name: "项目管理",
            // keepAlive:true
        },
        children : [{
            path     : "/Device",
            name     : "Device",
            component: () => import("../views/Device/Device.vue"),
            meta     : {
                requiresAuth: true,
                name        : "设备列表",
                keepAlive   : true
            },
        },
                    {
                        path     : "/RechargeService",
                        name     : "RechargeService",
                        component: () => import("../views/Device/RechargeService.vue"),
                        meta     : {
                            requiresAuth: true,
                            name        : "设备列表",
                            keepAlive   : true
                        },
                    },
        ],
    },
    {
        path     : "",
        name     : "RechargeService",
        component: Layout,
        meta     : {
            // requiresAuth: true,
            name: "业务充值",
            // keepAlive:true
        },
        children : [{
            path     : "/RechargeService",
            name     : "RechargeService",
            component: () => import("../views/Device/RechargeService.vue"),
            meta     : {
                requiresAuth: true,
                name        : "业务充值",
                keepAlive   : true
            },
        }, {
            path     : "/VipStock",
            name     : "VipStock",
            component: () => import("../views/VipStock/VipStock.vue"),
            meta     : {
                requiresAuth: true,
                name        : "库存设置",
                keepAlive   : true
            },
        },],
    },
    {
        path     : "",
        name     : "AppCenters",
        component: Layout,
        meta     : {
            // requiresAuth: true,
            name: "应用中心",
            // keepAlive:true
        },
        children : [{
            path     : "/AppCenter",
            name     : "AppCenter",
            component: () => import("../views/AppCenter/AppCenter.vue"),
            meta     : {
                requiresAuth: true,
                name        : "应用列表",
                keepAlive   : true
            },
        }, {
            path     : "/AppCenterSource",
            name     : "AppCenterSource",
            component: () => import("../views/AppCenter/AppCenterSource.vue"),
            meta     : {
                requiresAuth: true,
                name        : "用户自定义上传",
                keepAlive   : true
            },
        },]
    },
    {
        path     : "",
        name     : "MsgLogs",
        component: Layout,
        meta     : {
            // requiresAuth: true,
            name: "日志列表",
            // keepAlive:true
        },
        children : [{
            path     : "/ClientLog",
            name     : "ClientLog",
            component: () => import("../views/Logs/ClientLog.vue"),
            meta     : {
                requiresAuth: true,
                name        : "消息日志",
                keepAlive   : true
            },
        }, {
            path     : "/OperateLog",
            name     : "OperateLog",
            component: () => import("../views/Logs/OperateLog.vue"),
            meta     : {
                requiresAuth: true,
                name        : "操作日志",
                keepAlive   : true
            },
        },],
    },
    {
        path     : "",
        name     : "Orders",
        component: Layout,
        meta     : {
            // requiresAuth: true,
            name: "订单管理",
            // keepAlive:true
        },
        children : [{
            path     : "/Order",
            name     : "Order",
            component: () => import("../views/Orders/Order.vue"),
            meta     : {
                requiresAuth: true,
                name        : "订单列表",
                keepAlive   : true
            },
        },],
    },
    {
        path     : "",
        name     : "Auths",
        component: Layout,
        meta     : {
            name: "用户权限",
        },
        children : [
            {
                path     : "/Role",
                name     : "Role",
                component: () => import("../views/Auth/Role.vue"),
                meta     : {
                    requiresAuth: true,
                    name        : "角色列表",
                    keepAlive   : true
                },
            }
            , {
                path     : "/User",
                name     : "User",
                component: () => import("../views/Auth/User.vue"),
                meta     : {
                    requiresAuth: true,
                    name        : "用户列表",
                    keepAlive   : true
                },
            }
            , {
                path     : "/Permission",
                name     : "Permission",
                component: () => import("../views/Auth/Permission.vue"),
                meta     : {
                    requiresAuth: true,
                    name        : "权限列表",
                    keepAlive   : true
                },
            }
            , {
                path     : "/UserProfile",
                name     : "UserProfile",
                component: () => import("../views/Auth/UserProfile.vue"),
                meta     : {
                    requiresAuth: true,
                    name        : "个人信息",
                    keepAlive   : true
                },
            }
        ]
    },
];

const router = createRouter({
    history: createWebHashHistory(),
    routes,
});

export default router;